import React from 'react';
import { observer } from 'mobx-react';
import { Dialog } from 'primereact/dialog';
import { Divider } from 'primereact/divider';
import PropTypes from 'prop-types';
import { DATE_FORMAT } from 'fc/utils/date';
import { getValue, getValueByKey, getValueDate } from 'fc/utils/utils';
import DadosEstaticosService from '~/services/DadosEstaticosService';
import MultipleFileUploader from 'fc/components/MultipleFileUploader';
import ComissaoService from '~/services/ComissaoService';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

@observer
class ComissaoDetailDialog extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      comissaoDetalhes: null,
      loading: true,
      arquivos: [],
    };
    this.comissaoService = new ComissaoService();
  }

  componentDidMount() {
    if (this.props.comissao?.id) {
      this.carregarDetalhesComissao();
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.comissao?.id !== this.props.comissao?.id && this.props.comissao?.id) {
      this.carregarDetalhesComissao();
    }
  }

  async carregarDetalhesComissao() {
    try {
      this.setState({ loading: true });

      const responseComissao = await this.comissaoService.getById(this.props.comissao.id);

      const responseArquivos = await this.comissaoService.recuperarArquivos(this.props.comissao.id);

      this.setState({
        comissaoDetalhes: responseComissao.data,
        arquivos: responseArquivos.data || [],
        loading: false,
      });
    } catch (error) {
      console.error('Erro ao carregar detalhes da comissão:', error);
      this.setState({ loading: false });
    }
  }

  _renderValue(label, value, col = 12) {
    return (
      <div className={`p-mt-3 p-col-12 p-md-${col} p-sm-${col >= 6 ? col : 6}`}>
        <div className="p-col-12 drawer-content-label font-bold">{label}</div>
        <div className={`p-col-12`}>{value ? value : '-'}</div>
      </div>
    );
  }

  _renderMembros(membros) {
    if (!membros || membros.length === 0) {
      return <div>Nenhum membro cadastrado</div>;
    }

    return (
      <DataTable value={membros} size="small" stripedRows>
        <Column field="nome" header="Nome" />
        <Column field="cpf" header="CPF" />
        <Column field="email" header="E-mail" />
      </DataTable>
    );
  }

  _renderArquivos() {
    if (!this.state.arquivos || this.state.arquivos.length === 0) {
      return <div>Nenhum arquivo cadastrado</div>;
    }

    const mockFileStore = {
      uploadedFiles: this.state.arquivos.map((arquivo) => ({
        arquivo: {
          id: arquivo.idArquivo,
          lookupId: arquivo.lookupId,
          nomeOriginal: arquivo.nomeOriginal,
          tipoArquivo: arquivo.tipoArquivo,
        },
        tipo: arquivo.tipoArquivo,
        descricaoTipoOutros: arquivo.descricaoTipoOutros,
        key: arquivo.idArquivo,
      })),
      keyedUploadedFiles: this.state.arquivos.map((arquivo) => ({
        arquivo: {
          id: arquivo.idArquivo,
          lookupId: arquivo.lookupId,
          nomeOriginal: arquivo.nomeOriginal,
          tipoArquivo: arquivo.tipoArquivo,
        },
        tipo: arquivo.tipoArquivo,
        descricaoTipoOutros: arquivo.descricaoTipoOutros,
        key: arquivo.idArquivo,
      })),
      downloadFile: (fileDTO) => {
        this.comissaoService
          .download(fileDTO)
          .then((response) => {
            const link = document.createElement('a');
            link.setAttribute('href', URL.createObjectURL(response.data));
            link.setAttribute('download', fileDTO.nomeOriginal);
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          })
          .catch((error) => {
            console.error('Erro ao fazer download do arquivo:', error);
          });
      },
    };

    return (
      <MultipleFileUploader
        fileTypes={DadosEstaticosService.getTipoArquivoComissao()}
        downloadOnly
        store={mockFileStore}
      />
    );
  }

  render() {
    const { comissao, visible, onHide } = this.props;
    const { comissaoDetalhes, loading } = this.state;

    const header = (
      <div style={{ width: '100%', textAlign: 'left' }}>
        <h4 style={{ margin: 0 }}>Detalhes da Comissão</h4>
      </div>
    );

    if (!comissao) {
      return null;
    }

    return (
      <Dialog
        header={header}
        visible={visible}
        style={{ width: '70%' }}
        breakpoints={{ '960px': '85vw', '640px': '95vw' }}
        onHide={onHide}
        draggable={false}
        resizable={false}
        dismissableMask
      >
        <div>
          <Divider />
          {loading ? (
            <div className="p-text-center p-mt-4">Carregando...</div>
          ) : (
            <div className="p-grid">
              {/* Dados Básicos */}
              <div className="p-col-12">
                <h5>Dados Básicos</h5>
              </div>
              {this._renderValue('Número', getValue(comissaoDetalhes?.numero || comissao.numero), 6)}
              {this._renderValue(
                'Tipo',
                getValueByKey(comissaoDetalhes?.tipo || comissao.tipo, DadosEstaticosService.getTipoComissao()),
                6
              )}
              {this._renderValue(
                'Tipo de Conjunto',
                getValueByKey(comissaoDetalhes?.tipoConjunto, DadosEstaticosService.getTipoConjuntoComissao()),
                6
              )}
              {this._renderValue('Entidade', getValue(comissaoDetalhes?.entidade?.nome), 6)}
              {this._renderValue(
                'Data de Vigência Inicial',
                getValueDate(comissaoDetalhes?.dataVigenciaInicial, DATE_FORMAT),
                6
              )}
              {this._renderValue(
                'Data de Vigência Final',
                getValueDate(comissaoDetalhes?.dataVigenciaFinal, DATE_FORMAT),
                6
              )}

              {/* Membros */}
              <div className="p-col-12 p-mt-4">
                <h5>Membros da Comissão</h5>
                {this._renderMembros(comissaoDetalhes?.membros)}
              </div>

              {/* Arquivos */}
              <div className="p-col-12 p-mt-4">
                <h5>Documentos</h5>
                {this._renderArquivos()}
              </div>
            </div>
          )}
        </div>
      </Dialog>
    );
  }
}

ComissaoDetailDialog.propTypes = {
  comissao: PropTypes.object,
  visible: PropTypes.bool.isRequired,
  onHide: PropTypes.func.isRequired,
};

export default ComissaoDetailDialog;
